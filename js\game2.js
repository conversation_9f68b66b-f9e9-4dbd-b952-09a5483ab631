// Game state
const gameState = {
    playerMaxHealth: 100,
    playerHealth: 100,
    enemyMaxHealth: 100,
    enemyHealth: 100,
    currentQuestion: 0,
    timer: null,
    timeLeft: 10,
    isGameActive: false,
    correctAnswers: 0,
    totalQuestions: 0,
    powerUps: {
        addLife: { count: 1, active: false },
        protect: { count: 1, active: false },
        addTime: { count: 1, active: false }
    },
    isProtected: false
};

// Store questions from database
let gameData = [];

// Sound Effects
const correctSound = new Audio('../sounds/correct2.mp3');
const wrongSound = new Audio('../sounds/wrong2.mp3');
const successSound = new Audio('../sounds/success.mp3');

// Set initial volume for all sound effects
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
successSound.volume = 0.3;

// DOM Elements
const questionText = document.querySelector('.question-text');
const optionButtons = document.querySelectorAll('.option-btn');
const timerBar = document.querySelector('.timer-bar');
const timerText = document.querySelector('.timer-text');
const playerHealthBar = document.querySelector('.player-health');
const enemyHealthBar = document.querySelector('.enemy-health');
const playerHealthValue = document.querySelector('.player-side .health-value');
const enemyHealthValue = document.querySelector('.enemy-side .health-value');
const playerRobot = document.querySelector('.player-robot');
const enemyRobot = document.querySelector('.enemy-robot');
const instructionsModal = document.querySelector('.instructions-modal');
const resultsModal = document.querySelector('.results-modal');
const resultTitle = document.querySelector('.result-title');
const resultMessage = document.querySelector('.result-message');
const startButton = document.querySelector('.start-btn');
const replayButton = document.querySelector('.replay-btn');
const mainMenuButton = document.querySelector('.main-menu-btn');

// Power Up Elements
const powerUpButtons = {
    addLife: document.getElementById('add-life'),
    protect: document.getElementById('protect'),
    addTime: document.getElementById('add-time')
};
const powerUpCounts = {
    addLife: document.querySelector('#add-life .power-up-count'),
    protect: document.querySelector('#protect .power-up-count'),
    addTime: document.querySelector('#add-time .power-up-count')
};

// Initialize game
async function initGame() {
    try {
        // Reset game state including power-ups
        gameState.playerHealth = gameState.playerMaxHealth;
        gameState.enemyHealth = gameState.enemyMaxHealth;
        gameState.currentQuestion = 0;
        gameState.timeLeft = 10;
        gameState.isGameActive = false;
        gameState.correctAnswers = 0;
        gameState.isProtected = false;
        
        // Reset power-ups
        gameState.powerUps = {
            addLife: { count: 1, active: false },
            protect: { count: 1, active: false },
            addTime: { count: 1, active: false }
        };
        
        // Reset robot expressions and damage states
        playerRobot.classList.remove('damaged', 'critical', 'victory', 'defeat', 'hurt');
        enemyRobot.classList.remove('damaged', 'critical', 'victory', 'defeat', 'hurt');
        
        // Update UI
        updatePowerUpUI();
        updateHealthBars();
        
        // Get the level from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const level = parseInt(urlParams.get('level')) || 1; // Default to level 1 if not set

        console.log('Current URL:', window.location.href); // Debug log
        console.log('Level parameter from URL:', urlParams.get('level')); // Debug log
        console.log('Parsed level:', level); // Debug log

        // Fetch questions from the database
        const response = await fetch(`../php/game.php?level=${level}`);
        const result = await response.json();
        

        console.log('Fetched data:', result); // Debug log

        if (result.success) {
            gameData = result.data;
            console.log('Game data loaded:', gameData); // Debug log

            if (gameData.length === 0) {
                console.error('No questions found in the database');
                alert('No questions found. Please try again later.');
                return;
            }

            // Initialize game state
            gameState.playerHealth = gameState.playerMaxHealth;
            gameState.enemyHealth = gameState.enemyMaxHealth;
            gameState.currentQuestion = 0;
            gameState.isGameActive = false;
            gameState.correctAnswers = 0;
            gameState.totalQuestions = gameData.length;

            updateHealthBars();
            loadQuestion();
            // Don't start timer here - wait for start button click

            // Reset timer display
            gameState.timeLeft = 15;
            timerText.textContent = gameState.timeLeft;
            timerBar.style.width = '100%';

            // Show instructions modal on first load
            instructionsModal.style.display = 'flex';
        } else {
            console.error('Failed to load game data:', result.error);
            alert('Failed to load game data. Please try again later.');
        }
    } catch (error) {
        console.error('Error loading game data:', error);
        alert('Error loading game data. Please try again later.');
    }
}

// Load question
function loadQuestion() {
    // Check if we have questions loaded
    if (gameData.length === 0) {
        console.error('No questions available');
        return;
    }

    // Check if we've completed all questions
    if (gameState.currentQuestion >= gameData.length) {
        endGame(true); // Player wins if they complete all questions
        return;
    }

    // Make the game active for the new question
    gameState.isGameActive = true;

    const currentQ = gameData[gameState.currentQuestion];
    console.log('Current question:', currentQ); // Debug log

    if (!currentQ || !currentQ.question_text) {
        console.error('Invalid question data:', currentQ);
        return;
    }

    questionText.textContent = currentQ.question_text;

    // Create options from the database fields
    const options = [
        currentQ.option1,
        currentQ.option2,
        currentQ.option3,
        currentQ.option4
    ];

    optionButtons.forEach((button, index) => {
        if (!options[index]) {
            console.error('Missing option at index:', index);
            return;
        }

        button.textContent = options[index];
        button.disabled = false;
        button.classList.remove('correct', 'incorrect');
    });
}

// Start timer
function startTimer() {
    gameState.timeLeft = 15;
    timerText.textContent = gameState.timeLeft;
    timerBar.style.width = '100%';

    if (gameState.timer) {
        clearInterval(gameState.timer);
    }

    gameState.timer = setInterval(() => {
        gameState.timeLeft--;
        timerText.textContent = gameState.timeLeft;
        timerBar.style.width = `${(gameState.timeLeft / 15) * 100}%`;

        if (gameState.timeLeft <= 0) {
            clearInterval(gameState.timer);
            handleTimeout();
        }
    }, 1000);
}

// Update health bars
function updateHealthBars() {
    // Update visual health bars
    playerHealthBar.style.width = `${(gameState.playerHealth / gameState.playerMaxHealth) * 100}%`;
    enemyHealthBar.style.width = `${(gameState.enemyHealth / gameState.enemyMaxHealth) * 100}%`;

    // Update health text values
    playerHealthValue.textContent = `${gameState.playerHealth}/${gameState.playerMaxHealth}`;
    enemyHealthValue.textContent = `${gameState.enemyHealth}/${gameState.enemyMaxHealth}`;

    // Update robot facial expressions and damage states based on health
    updateRobotExpressions();

    // Check for game end conditions
    if (gameState.playerHealth <= 0) {
        endGame(false);
    } else if (gameState.enemyHealth <= 0) {
        endGame(true);
    }
}

// Update robot facial expressions based on health
function updateRobotExpressions() {
    const playerHealthPercent = (gameState.playerHealth / gameState.playerMaxHealth) * 100;
    const enemyHealthPercent = (gameState.enemyHealth / gameState.enemyMaxHealth) * 100;

    // Remove all damage classes first
    playerRobot.classList.remove('damaged', 'critical', 'victory', 'defeat');
    enemyRobot.classList.remove('damaged', 'critical', 'victory', 'defeat');

    // Update player robot expressions
    if (playerHealthPercent <= 0) {
        playerRobot.classList.add('defeat');
    } else if (playerHealthPercent <= 30) {
        playerRobot.classList.add('critical');
    } else if (playerHealthPercent <= 60) {
        playerRobot.classList.add('damaged');
    }

    // Update enemy robot expressions
    if (enemyHealthPercent <= 0) {
        enemyRobot.classList.add('defeat');
    } else if (enemyHealthPercent <= 30) {
        enemyRobot.classList.add('critical');
    } else if (enemyHealthPercent <= 60) {
        enemyRobot.classList.add('damaged');
    }
}

// Helper to create and animate a laser beam
function showLaserBeam(fromRobot, toRobot, color) {
    // Get bounding rectangles
    const fromHead = fromRobot.querySelector('.robot-head');
    const toHead = toRobot.querySelector('.robot-head');
    const fromHeadRect = fromHead.getBoundingClientRect();
    const toHeadRect = toHead.getBoundingClientRect();
    
    // Calculate coordinates relative to viewport
    const startX = fromHeadRect.left + fromHeadRect.width / 2;
    const startY = fromHeadRect.top + fromHeadRect.height / 2;
    const endX = toHeadRect.left + toHeadRect.width / 2;
    const endY = toHeadRect.top + toHeadRect.height / 2;

    // Create charging effect at the source
    createChargingEffect(fromRobot, color);

    // Wait for charging animation, then fire laser
    setTimeout(() => {
        // Create the main laser beam
        createLaserBeam(startX, startY, endX, endY, color);
        
        // Create hit effect at target
        setTimeout(() => {
            createHitEffect(endX, endY, color);
        }, 350);
    }, 500);
}

// Create charging effect at the robot's eyes
function createChargingEffect(robot, color) {
    const eyes = robot.querySelectorAll('.robot-eye');
    
    eyes.forEach(eye => {
        // Add charging glow to eyes
        eye.style.boxShadow = `0 0 20px ${color}, 0 0 40px ${color}`;
        eye.style.background = color;
        eye.style.animation = 'charging-pulse 0.5s ease-in-out';
    });

    // Add charging particles around the robot
    const particleContainer = document.createElement('div');
    particleContainer.style.position = 'fixed';
    particleContainer.style.left = '0';
    particleContainer.style.top = '0';
    particleContainer.style.width = '100vw';
    particleContainer.style.height = '100vh';
    particleContainer.style.pointerEvents = 'none';
    particleContainer.style.zIndex = '9998';
    document.body.appendChild(particleContainer);

    // Create charging particles
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            const robotRect = robot.getBoundingClientRect();
            const centerX = robotRect.left + robotRect.width / 2;
            const centerY = robotRect.top + robotRect.height / 2;
            
            particle.style.position = 'fixed';
            particle.style.left = (centerX + (Math.random() - 0.5) * 100) + 'px';
            particle.style.top = (centerY + (Math.random() - 0.5) * 100) + 'px';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 10px ${color}`;
            particle.style.animation = 'particle-charge 0.5s ease-out forwards';
            particleContainer.appendChild(particle);
            
            setTimeout(() => particle.remove(), 500);
        }, i * 50);
    }

    // Remove charging effect after animation
    setTimeout(() => {
        eyes.forEach(eye => {
            eye.style.boxShadow = '';
            eye.style.background = '';
            eye.style.animation = '';
        });
        particleContainer.remove();
    }, 500);
}

// Create the main laser beam
function createLaserBeam(startX, startY, endX, endY, color) {
    // Create SVG container
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.position = 'fixed';
    svg.style.left = '0';
    svg.style.top = '0';
    svg.style.width = '100vw';
    svg.style.height = '100vh';
    svg.style.pointerEvents = 'none';
    svg.style.zIndex = '9999';
    svg.classList.add('laser-beam-svg');

    // Create main laser line
    const mainLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    mainLine.setAttribute('x1', startX);
    mainLine.setAttribute('y1', startY);
    mainLine.setAttribute('x2', startX);
    mainLine.setAttribute('y2', startY);
    mainLine.setAttribute('stroke', color);
    mainLine.setAttribute('stroke-width', '12');
    mainLine.setAttribute('stroke-linecap', 'round');
    mainLine.setAttribute('filter', 'drop-shadow(0 0 20px ' + color + ')');
    mainLine.style.opacity = '0';
    svg.appendChild(mainLine);

    // Create outer glow line
    const glowLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    glowLine.setAttribute('x1', startX);
    glowLine.setAttribute('y1', startY);
    glowLine.setAttribute('x2', startX);
    glowLine.setAttribute('y2', startY);
    glowLine.setAttribute('stroke', color);
    glowLine.setAttribute('stroke-width', '20');
    glowLine.setAttribute('stroke-linecap', 'round');
    glowLine.setAttribute('filter', 'blur(8px)');
    glowLine.style.opacity = '0.3';
    svg.appendChild(glowLine);

    document.body.appendChild(svg);

    // Animate laser firing
    setTimeout(() => {
        // Fade in
        mainLine.style.transition = 'opacity 0.1s';
        glowLine.style.transition = 'opacity 0.1s';
        mainLine.style.opacity = '1';
        glowLine.style.opacity = '0.3';

        // Extend to target
        setTimeout(() => {
            mainLine.setAttribute('x2', endX);
            mainLine.setAttribute('y2', endY);
            glowLine.setAttribute('x2', endX);
            glowLine.setAttribute('y2', endY);
        }, 50);
    }, 10);

    // Add laser trail particles
    createLaserTrail(startX, startY, endX, endY, color);

    // Fade out and remove laser
    setTimeout(() => {
        mainLine.style.transition = 'opacity 0.3s';
        glowLine.style.transition = 'opacity 0.3s';
        mainLine.style.opacity = '0';
        glowLine.style.opacity = '0';
        setTimeout(() => {
            svg.remove();
        }, 300);
    }, 400);
}

// Create laser trail particles
function createLaserTrail(startX, startY, endX, endY, color) {
    const trailContainer = document.createElement('div');
    trailContainer.style.position = 'fixed';
    trailContainer.style.left = '0';
    trailContainer.style.top = '0';
    trailContainer.style.width = '100vw';
    trailContainer.style.height = '100vh';
    trailContainer.style.pointerEvents = 'none';
    trailContainer.style.zIndex = '9997';
    document.body.appendChild(trailContainer);

    // Create trail particles along the laser path
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const progress = i / 14;
            const x = startX + (endX - startX) * progress;
            const y = startY + (endY - startY) * progress;
            
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.left = (x - 2) + 'px';
            particle.style.top = (y - 2) + 'px';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 8px ${color}`;
            particle.style.animation = 'trail-fade 0.4s ease-out forwards';
            trailContainer.appendChild(particle);
            
            setTimeout(() => particle.remove(), 400);
        }, i * 20);
    }

    setTimeout(() => trailContainer.remove(), 600);
}

// Create hit effect at target
function createHitEffect(x, y, color) {
    // Create main hit burst
    const hit = document.createElement('div');
    hit.className = 'laser-hit-effect';
    hit.style.position = 'fixed';
    hit.style.left = (x - 30) + 'px';
    hit.style.top = (y - 30) + 'px';
    hit.style.width = '60px';
    hit.style.height = '60px';
    hit.style.borderRadius = '50%';
    hit.style.background = `radial-gradient(circle, ${color} 0%, rgba(255,255,255,0.8) 30%, transparent 70%)`;
    hit.style.boxShadow = `0 0 40px 15px ${color}`;
    hit.style.opacity = '0';
    hit.style.pointerEvents = 'none';
    hit.style.zIndex = '10000';
    hit.style.animation = 'laser-hit-burst 0.5s ease-out forwards';
    document.body.appendChild(hit);

    // Create shockwave rings
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const ring = document.createElement('div');
            ring.style.position = 'fixed';
            ring.style.left = (x - 20) + 'px';
            ring.style.top = (y - 20) + 'px';
            ring.style.width = '40px';
            ring.style.height = '40px';
            ring.style.border = `2px solid ${color}`;
            ring.style.borderRadius = '50%';
            ring.style.opacity = '0.8';
            ring.style.pointerEvents = 'none';
            ring.style.zIndex = '9999';
            ring.style.animation = 'shockwave 0.6s ease-out forwards';
            document.body.appendChild(ring);
            
            setTimeout(() => ring.remove(), 600);
        }, i * 100);
    }

    // Create impact particles
    for (let i = 0; i < 12; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            const angle = (i / 12) * Math.PI * 2;
            const distance = 40 + Math.random() * 20;
            const px = x + Math.cos(angle) * distance;
            const py = y + Math.sin(angle) * distance;
            
            // Set random direction for particle movement
            const dx = (Math.random() - 0.5) * 60;
            const dy = (Math.random() - 0.5) * 60;
            
            particle.style.position = 'fixed';
            particle.style.left = (px - 2) + 'px';
            particle.style.top = (py - 2) + 'px';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 6px ${color}`;
            particle.style.setProperty('--dx', dx + 'px');
            particle.style.setProperty('--dy', dy + 'px');
            particle.style.animation = 'impact-particle 0.8s ease-out forwards';
            document.body.appendChild(particle);
            
            setTimeout(() => particle.remove(), 800);
        }, i * 30);
    }

    setTimeout(() => hit.remove(), 500);
}

// Handle player attack
function playerAttack() {
    // Only proceed if game is active
    if (!gameState.isGameActive) return;
    
    // Play attack animation
    playerRobot.classList.add('attack-animation');
    
    // Show laser beam from player to enemy
    showLaserBeam(playerRobot, enemyRobot, '#00F0FF');
    
    // Calculate damage based on total questions (enemy health / total questions)
    const damage = Math.ceil(100 / gameState.totalQuestions);
    
    // Apply damage after animation delay (increased for enhanced effects)
    setTimeout(() => {
        // Apply damage to enemy
        gameState.enemyHealth -= damage;
        
        // Ensure health doesn't go below 0
        if (gameState.enemyHealth < 0) {
            gameState.enemyHealth = 0;
        }
        
        // Add hurt animation to enemy
        enemyRobot.classList.add('hurt');
        setTimeout(() => {
            enemyRobot.classList.remove('hurt');
        }, 400);
        
        // Update health display
        updateHealthBars();
        
        // Check if enemy is defeated
        if (gameState.enemyHealth <= 0) {
            endGame(true);
        }
        
        // Remove attack animation
        playerRobot.classList.remove('attack-animation');
        
        // Load next question after attack animation
        loadNextQuestion();
    }, 800); // Increased delay for enhanced laser effects
}

// Handle enemy attack
function enemyAttack() {
    // Only proceed if game is active
    if (!gameState.isGameActive) return;
    
    // Play attack animation
    enemyRobot.classList.add('attack-animation');
    
    // Show laser beam from enemy to player
    showLaserBeam(enemyRobot, playerRobot, '#FF0055');
    
    // Calculate damage: 35 base damage with 20% chance for critical hit (x2)
    let damage = 35;
    const isCritical = Math.random() < 0.2; // 20% chance
    if (isCritical) {
        damage *= 2; // Critical hit doubles damage
    }
    
    // Apply damage after animation delay (increased for enhanced effects)
    setTimeout(() => {
        // Check if player is protected
        if (gameState.isProtected) {
            // Player is protected, block the attack
            gameState.isProtected = false;
            updatePowerUpUI();
            
            // Show protection effect
            const protectionEffect = document.createElement('div');
            protectionEffect.className = 'protection-effect';
            playerRobot.appendChild(protectionEffect);
            setTimeout(() => {
                protectionEffect.remove();
            }, 1000);
            return;
        }
        
        // Apply damage to player
        gameState.playerHealth -= damage;
        
        // Ensure health doesn't go below 0
        if (gameState.playerHealth < 0) {
            gameState.playerHealth = 0;
        }
        
        // Add hurt animation to player
        playerRobot.classList.add('hurt');
        setTimeout(() => {
            playerRobot.classList.remove('hurt');
        }, 400);
        
        // Update health display
        updateHealthBars();
        
        // Check for game over
        if (gameState.playerHealth <= 0) {
            endGame(false);
        }
        
        // Remove attack animation class
        enemyRobot.classList.remove('attack-animation');
        
        // Only load next question if the game is still active
        if (gameState.playerHealth > 0) {
            loadNextQuestion();
        }
    }, 800); // Increased delay for enhanced laser effects
}

// Load the next question or end the game if all questions are answered
function loadNextQuestion() {
    // Move to next question
    gameState.currentQuestion++;
    
    // Check if we've completed all questions
    if (gameState.currentQuestion >= gameData.length) {
        // Only win if all questions were answered correctly
        if (gameState.correctAnswers === gameState.totalQuestions) {
            endGame(true);
        } else {
            // If not all questions were answered correctly, it's a loss
            endGame(false);
        }
    } else {
        // Load the next question and start the timer
        loadQuestion();
        startTimer();
    }
}

// Handle answer selection
function handleAnswer(index) {
    // Prevent multiple answers
    if (!gameState.isGameActive) return;

    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);

    // Stop the timer
    clearInterval(gameState.timer);

    // Get current question
    const currentQ = gameData[gameState.currentQuestion];

    // Get the correct answer index (subtracting 1 since database uses 1-based indexing)
    const correctAnswerIndex = parseInt(currentQ.correct_answer) - 1;

    // Check if answer is correct
    if (index === correctAnswerIndex) {
        // Highlight correct answer
        optionButtons[index].classList.add('correct');

        // Increment correct answers
        gameState.correctAnswers++;

        // Play correct sound
        playSound(correctSound);

        // Increase user exp by 10 for correct answer
        const expEarned = 10;

        // Send exp data to savetodb.php
        fetch('../php/savetodb.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expEarned: expEarned,
                score: 0, // We'll update the final score at the end
                starsCount: 0, // We'll update the stars at the end
                level: parseInt(document.body.dataset.level) // Get level from body data-level attribute
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Failed to save exp:', data.error);
            }
        })
        .catch(error => {
            console.error('Error saving exp:', error);
        });

        // Player attacks enemy
        playerAttack();
    } else {
        // Highlight correct and incorrect answers
        optionButtons[index].classList.add('incorrect');
        optionButtons[correctAnswerIndex].classList.add('correct');

        // Play wrong sound
        playSound(wrongSound);

        // Enemy attacks player
        enemyAttack();
        
        // Add the current question to the end of the list to repeat it later
        gameData.push(currentQ);
        gameState.totalQuestions = gameData.length;
        
        // Don't advance to next question - repeat the same question
        // Reset buttons and timer for the same question
        setTimeout(() => {
            optionButtons.forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('correct', 'incorrect');
            });
            startTimer();
        }, 1000);
    }
}

// Handle timeout (no answer selected)
function handleTimeout() {
    // Prevent multiple timeouts
    if (!gameState.isGameActive) return;

    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);

    // Highlight correct answer
    const currentQ = gameData[gameState.currentQuestion];
    // Get the correct answer index (subtracting 1 since database uses 1-based indexing)
    const correctAnswerIndex = parseInt(currentQ.correct_answer) - 1;
    optionButtons[correctAnswerIndex].classList.add('correct');

    // Play wrong sound for timeout
    playSound(wrongSound);

    // Enemy attacks player
    enemyAttack();
    
    // Don't advance to next question - repeat the same question
    // Reset buttons and timer for the same question
    setTimeout(() => {
        optionButtons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('correct', 'incorrect');
        });
        startTimer();
    }, 1000);
}

// Play Sound Effect
function playSound(sound) {
    // Reset the sound to the beginning
    sound.currentTime = 0;
    // Play the sound
    sound.play().catch(e => console.log('Audio play failed:', e));
}

// End game
function endGame(playerWon) {
    // Set game as inactive
    gameState.isGameActive = false;

    // Clear any running timers
    clearInterval(gameState.timer);

    // Add victory/defeat expressions
    if (playerWon) {
        playerRobot.classList.add('victory');
        enemyRobot.classList.add('defeat');
    } else {
        playerRobot.classList.add('defeat');
        enemyRobot.classList.add('victory');
    }

    // Show results with proper calculation
    setTimeout(() => {
        showResults(playerWon);
    }, 1000);
}

// Show Results with Star Rating
function showResults(success) {
    const totalQuestions = gameState.totalQuestions;
    const correctAnswers = gameState.correctAnswers;
    const playerHealth = gameState.playerHealth;
    const totalExp = correctAnswers * 10; // 10 exp per correct answer

    // Calculate stars based on player health
    let stars;
    if (!success) {
        stars = 0;
    } else if (playerHealth >= 100) {
        stars = 3;
    } else if (playerHealth >= 41 && playerHealth <= 70) {
        stars = 2;
    } else if (playerHealth < 41) {
        stars = 1;
    } else {
        stars = 0;
    }

    // Update modal content
    const resultTitle = document.getElementById('result-title');
    const resultMessage = document.getElementById('result-message');
    const resultStars = document.getElementById('result-stars');
    const expText = document.querySelector('.exp-text');

    if (success) {
        resultTitle.textContent = 'VICTORY!';
        resultMessage.textContent = `You defeated the enemy robot! Answered all ${totalQuestions} questions correctly with ${playerHealth} health remaining!`;

        // Add celebration animation and success sound
        if (playerRobot) {
            playerRobot.style.animation = 'victory-dance 2s ease-in-out infinite';
        }
        playSound(successSound);
    } else {
        resultTitle.textContent = 'DEFEAT!';
        if (playerHealth <= 0) {
            resultMessage.textContent = 'Your robot was destroyed! You need to answer all questions correctly to win!';
        } else {
            resultMessage.textContent = 'You ran out of questions! You need to answer all questions correctly to win!';
        }
    }

    // Display stars with enhanced animation
    if (resultStars) {
        resultStars.innerHTML = '';
        resultStars.classList.add('animate-in');

        // Add perfect score celebration for 3 stars
        if (stars === 3) {
            resultStars.classList.add('perfect-score');
        }

        for (let i = 0; i < 3; i++) {
            const star = document.createElement('span');
            star.textContent = i < stars ? '★' : '☆';
            star.style.color = i < stars ? '#ffeb3b' : '#555';
            resultStars.appendChild(star);
        }

        // Remove animation class after animation completes
        setTimeout(() => {
            resultStars.classList.remove('animate-in');
        }, 1200);
    }

    // Update exp display with animation
    if (expText) {
        expText.textContent = `+${totalExp} EXP`;
        const expContainer = document.querySelector('.exp-container');
        if (expContainer) {
            expContainer.classList.add('animate-in');

            // Add perfect score celebration for 3 stars
            if (stars === 3) {
                expContainer.classList.add('perfect-score');
            }

            // Remove animation class after animation completes
            setTimeout(() => {
                expContainer.classList.remove('animate-in');
            }, 1000);
        }
    }

    // Send final game data to savetodb.php
    fetch('../php/savetodb.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            expEarned: 0, // We already sent exp per question
            score: correctAnswers,
            starsCount: stars,
            level: parseInt(document.body.dataset.level) // Get level from body data-level attribute
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to save final game data:', data.error);
        }
    })
    .catch(error => {
        console.error('Error saving final game data:', error);
    });

    // Show the modal
    resultsModal.style.display = 'flex';
}

// Shuffle array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Power Up Functions
function usePowerUp(type) {
    if (!gameState.isGameActive || gameState.powerUps[type].count <= 0) return;

    switch(type) {
        case 'addLife':
            gameState.playerHealth = Math.min(gameState.playerMaxHealth, gameState.playerHealth + 30);
            updateHealthBars();
            break;
            
        case 'protect':
            gameState.isProtected = true;
            break;
            
        case 'addTime':
            gameState.timeLeft += 10;
            timerText.textContent = gameState.timeLeft;
            timerBar.style.width = `${(gameState.timeLeft / 10) * 100}%`;
            break;
    }
    
    // Decrement power-up count and update UI
    gameState.powerUps[type].count--;
    updatePowerUpUI();
    
    // Visual feedback
    const button = powerUpButtons[type];
    button.classList.add('power-up-active');
    setTimeout(() => {
        button.classList.remove('power-up-active');
    }, 1000);
}

// Update power-up UI based on current state
function updatePowerUpUI() {
    for (const [type, data] of Object.entries(gameState.powerUps)) {
        const button = powerUpButtons[type];
        const countElement = powerUpCounts[type];
        
        // Update count display
        countElement.textContent = data.count;
        
        // Disable button if no more uses
        if (data.count <= 0) {
            button.disabled = true;
        } else {
            button.disabled = false;
        }
        
        // Update protect button appearance
        if (type === 'protect') {
            if (gameState.isProtected) {
                button.classList.add('power-up-active');
                button.title = 'Protected!';
            } else {
                button.classList.remove('power-up-active');
                button.title = 'Protect (Immune to next attack)';
            }
        }
    }
}

// Event Listeners
optionButtons.forEach((button, index) => {
    button.addEventListener('click', () => handleAnswer(index));
});

// Add event listeners for power-up buttons
Object.entries(powerUpButtons).forEach(([type, button]) => {
    button.addEventListener('click', () => usePowerUp(type));
});

startButton.addEventListener('click', () => {
    instructionsModal.style.display = 'none';
    gameState.isGameActive = true;
    startTimer();
});

replayButton.addEventListener('click', () => {
    resultsModal.style.display = 'none';
    initGame();
});

mainMenuButton.addEventListener('click', () => {
    window.location.href = '../html/mainpage.html';
});

// Initialize the game when the page loads
window.addEventListener('load', initGame);
